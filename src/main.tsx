import React from 'react';
import ReactDOM from 'react-dom/client';
import { ChakraProvider } from '@chakra-ui/react';
import App from './App';
import theme from './theme';
import { Toaster } from './ui/toaster'; // 使用简化版本

// 添加详细的错误调试
console.log('🔍 [MAIN] 开始应用初始化...');
console.log('🔍 [MAIN] React 版本:', React.version);
console.log('🔍 [MAIN] React 对象:', Object.keys(React));
console.log('🔍 [MAIN] React.Children:', typeof React.Children);
console.log('🔍 [MAIN] ReactDOM 版本:', ReactDOM.version);
console.log('🔍 [MAIN] 当前环境:', {
  userAgent: navigator.userAgent,
  platform: navigator.platform,
  language: navigator.language,
  cookieEnabled: navigator.cookieEnabled
});

// 检查根元素
const rootElement = document.getElementById('root');
console.log('🔍 [MAIN] 根元素:', rootElement);
if (!rootElement) {
  console.error('❌ [MAIN] 根元素不存在！');
  throw new Error('根元素 #root 不存在');
}

// 检查 React.Children 是否存在
if (!React.Children) {
  console.error('❌ [MAIN] React.Children 不存在！');
  console.error('❌ [MAIN] 这可能是React版本兼容性问题');
} else {
  console.log('✅ [MAIN] React.Children 存在:', Object.keys(React.Children));
}

// 添加错误边界
class ErrorBoundary extends React.Component {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: any) {
    console.error('🚨 [ERROR BOUNDARY] 捕获到错误:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error('🚨 [ERROR BOUNDARY] 错误详情:', error, errorInfo);
  }

  render() {
    if ((this.state as any).hasError) {
      return (
        <div style={{
          padding: '20px',
          color: 'red',
          fontFamily: 'monospace',
          backgroundColor: '#fff',
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <h2>应用启动错误</h2>
          <p>错误信息: {(this.state as any).error?.toString()}</p>
          <p>错误堆栈: {(this.state as any).error?.stack}</p>
          <p>请检查开发者工具控制台获取详细信息</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '10px 20px',
              marginTop: '20px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            重新加载应用
          </button>
        </div>
      );
    }
    return (this.props as any).children;
  }
}

// 添加全局错误处理
window.addEventListener('error', (event) => {
  console.error('🚨 [GLOBAL ERROR]', event.error);
  console.error('🚨 [GLOBAL ERROR] 文件:', event.filename);
  console.error('🚨 [GLOBAL ERROR] 行号:', event.lineno);
  console.error('🚨 [GLOBAL ERROR] 列号:', event.colno);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 [UNHANDLED REJECTION]', event.reason);
});

try {
  console.log('🔍 [MAIN] 创建 React 根元素...');
  const root = ReactDOM.createRoot(rootElement);
  console.log('🔍 [MAIN] 根元素创建成功，开始渲染...');

  root.render(
    <ErrorBoundary>
      <ChakraProvider value={theme}>
        <App />
        <Toaster />
      </ChakraProvider>
    </ErrorBoundary>
  );

  console.log('✅ [MAIN] 应用渲染完成');

  // 延迟检查渲染结果
  setTimeout(() => {
    const appContent = rootElement.innerHTML;
    console.log('🔍 [MAIN] 渲染后的内容长度:', appContent.length);
    if (appContent.length < 100) {
      console.warn('⚠️ [MAIN] 渲染内容可能不完整');
      console.log('🔍 [MAIN] 当前内容:', appContent.substring(0, 200));
    } else {
      console.log('✅ [MAIN] 渲染内容正常');
    }
  }, 1000);

} catch (error) {
  console.error('❌ [MAIN] 应用初始化失败:', error);
  console.error('❌ [MAIN] 错误堆栈:', error.stack);

  // 显示错误信息到页面
  if (rootElement) {
    rootElement.innerHTML = `
      <div style="padding: 20px; color: red; font-family: monospace; background: white; min-height: 100vh;">
        <h2>应用初始化失败</h2>
        <p><strong>错误信息:</strong> ${error.message}</p>
        <p><strong>错误堆栈:</strong></p>
        <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">${error.stack}</pre>
        <button onclick="window.location.reload()" style="padding: 10px 20px; margin-top: 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
          重新加载应用
        </button>
      </div>
    `;
  }
}
