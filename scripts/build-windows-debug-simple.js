#!/usr/bin/env node

/**
 * 简化的Windows调试版本构建脚本
 * 专门用于解决白屏和console问题
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建Windows调试版本...');

// 设置环境变量
process.env.DEBUG_MODE = 'true';
process.env.MEEA_WINDOWS_X64_DEBUG = 'true';
process.env.NODE_ENV = 'production';

console.log('📋 环境变量设置:');
console.log('  DEBUG_MODE:', process.env.DEBUG_MODE);
console.log('  MEEA_WINDOWS_X64_DEBUG:', process.env.MEEA_WINDOWS_X64_DEBUG);
console.log('  NODE_ENV:', process.env.NODE_ENV);

try {
  // 1. 生成版本信息
  console.log('\n📝 生成版本信息...');
  execSync('node scripts/generate-build-version.js', { stdio: 'inherit' });
  execSync('node scripts/inject-version.js', { stdio: 'inherit' });

  // 2. 构建前端
  console.log('\n🏗️ 构建前端应用...');
  execSync('yarn build', { stdio: 'inherit' });

  // 3. 验证Windows资源
  console.log('\n🔍 验证Windows资源...');
  execSync('node scripts/verify-windows-resources.js x64', { stdio: 'inherit' });

  // 4. 构建Windows包
  console.log('\n📦 构建Windows包...');
  execSync('electron-builder --win --x64 --config=electron-builder-win-x64.json --publish=never', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      DEBUG_MODE: 'true',
      MEEA_WINDOWS_X64_DEBUG: 'true'
    }
  });

  console.log('\n✅ Windows调试版本构建完成！');
  console.log('🐛 此版本包含以下调试功能:');
  console.log('  - 自动打开开发者工具');
  console.log('  - 详细的错误日志');
  console.log('  - F12快捷键支持');
  console.log('  - 右键菜单调试选项');

  // 检查构建结果
  const distDir = path.join(__dirname, '..', 'dist');
  const files = fs.readdirSync(distDir);
  const windowsFiles = files.filter(file => 
    file.includes('win') && file.endsWith('.exe')
  );

  if (windowsFiles.length > 0) {
    console.log('\n📦 构建产物:');
    windowsFiles.forEach(file => {
      const filePath = path.join(distDir, file);
      const stats = fs.statSync(filePath);
      const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
      console.log(`  📄 ${file} (${sizeMB} MB)`);
    });
  }

} catch (error) {
  console.error('\n❌ 构建失败:', error.message);
  process.exit(1);
}
