#!/usr/bin/env node

/**
 * Windows调试版本测试脚本
 * 用于验证白屏和console问题是否已修复
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 开始测试Windows调试版本...');

// 检查关键文件
function checkFiles() {
  console.log('\n📁 检查关键文件...');
  
  const files = [
    'dist/index.html',
    'dist/assets',
    'electron/main.js',
    'src/main.tsx'
  ];

  let allFilesExist = true;
  
  files.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      console.log(`  ✅ ${file}`);
    } else {
      console.log(`  ❌ ${file} - 文件不存在`);
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

// 检查main.js中的调试配置
function checkMainJsConfig() {
  console.log('\n🔍 检查main.js调试配置...');
  
  const mainJsPath = path.join(__dirname, '..', 'electron', 'main.js');
  const content = fs.readFileSync(mainJsPath, 'utf8');
  
  const checks = [
    {
      name: 'Windows平台开发者工具启用',
      pattern: /devToolsEnabled = isDev \|\| isDebugMode \|\| process\.platform === 'win32'/,
      required: true
    },
    {
      name: '强制打开开发者工具逻辑',
      pattern: /process\.platform === 'win32' \|\| isDebugMode/,
      required: true
    },
    {
      name: 'F12快捷键注册',
      pattern: /globalShortcut\.register\('F12'/,
      required: true
    }
  ];

  let allChecksPass = true;
  
  checks.forEach(check => {
    if (check.pattern.test(content)) {
      console.log(`  ✅ ${check.name}`);
    } else {
      console.log(`  ${check.required ? '❌' : '⚠️'} ${check.name}`);
      if (check.required) {
        allChecksPass = false;
      }
    }
  });

  return allChecksPass;
}

// 检查React应用的错误处理
function checkReactErrorHandling() {
  console.log('\n🔍 检查React错误处理...');
  
  const mainTsxPath = path.join(__dirname, '..', 'src', 'main.tsx');
  const content = fs.readFileSync(mainTsxPath, 'utf8');
  
  const checks = [
    {
      name: 'ErrorBoundary组件',
      pattern: /class ErrorBoundary extends React\.Component/,
      required: true
    },
    {
      name: '全局错误监听',
      pattern: /window\.addEventListener\('error'/,
      required: true
    },
    {
      name: '未处理Promise拒绝监听',
      pattern: /window\.addEventListener\('unhandledrejection'/,
      required: true
    },
    {
      name: '根元素检查',
      pattern: /document\.getElementById\('root'\)/,
      required: true
    }
  ];

  let allChecksPass = true;
  
  checks.forEach(check => {
    if (check.pattern.test(content)) {
      console.log(`  ✅ ${check.name}`);
    } else {
      console.log(`  ${check.required ? '❌' : '⚠️'} ${check.name}`);
      if (check.required) {
        allChecksPass = false;
      }
    }
  });

  return allChecksPass;
}

// 检查package.json脚本
function checkPackageScripts() {
  console.log('\n📦 检查package.json脚本...');
  
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const requiredScripts = [
    'build:windows-x64-debug',
    'build',
    'predist'
  ];

  let allScriptsExist = true;
  
  requiredScripts.forEach(script => {
    if (packageJson.scripts[script]) {
      console.log(`  ✅ ${script}`);
    } else {
      console.log(`  ❌ ${script} - 脚本不存在`);
      allScriptsExist = false;
    }
  });

  return allScriptsExist;
}

// 主测试函数
function runTests() {
  console.log('🧪 运行所有测试...\n');
  
  const results = {
    files: checkFiles(),
    mainJs: checkMainJsConfig(),
    react: checkReactErrorHandling(),
    scripts: checkPackageScripts()
  };

  console.log('\n📊 测试结果汇总:');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`  ${passed ? '✅' : '❌'} ${test}: ${passed ? '通过' : '失败'}`);
  });

  const allTestsPassed = Object.values(results).every(result => result);
  
  if (allTestsPassed) {
    console.log('\n🎉 所有测试通过！可以开始构建Windows调试版本。');
    console.log('\n📝 构建命令:');
    console.log('  yarn build:windows-x64-debug');
    console.log('  或者');
    console.log('  node scripts/build-windows-debug-simple.js');
  } else {
    console.log('\n❌ 部分测试失败，请检查上述问题后重新测试。');
  }

  return allTestsPassed;
}

// 运行测试
if (require.main === module) {
  const success = runTests();
  process.exit(success ? 0 : 1);
}

module.exports = { runTests };
